ARG CUDA_VERSION=12.3.1
# By copying the installation from a devel to a runtime container one could likely save a lot container size
FROM nvidia/cuda:$CUDA_VERSION-devel-ubuntu22.04 

ARG VMAF_TAG=master
ARG FFMPEG_TAG=master

RUN DEBIAN_FRONTEND=noninteractive apt-get update && DEBIAN_FRONTEND=noninteractive apt-get install -y libopenjp2-7-dev \
    ninja-build cmake git python3 python3-pip nasm xxd pkg-config curl unzip

RUN git clone https://github.com/Netflix/vmaf.git && cd vmaf && git checkout $VMAF_TAG

RUN git clone https://github.com/FFmpeg/FFmpeg.git && cd FFmpeg && git checkout $FFMPEG_TAG

RUN git clone https://github.com/FFmpeg/nv-codec-headers.git && cd nv-codec-headers && make && make install

# install vmaf
RUN python3 -m pip install meson
RUN cd vmaf && meson libvmaf/build libvmaf -Denable_cuda=true -Denable_avx512=true --buildtype release && \
    ninja -vC libvmaf/build  && \
    ninja -vC libvmaf/build  install

# install ffmpeg
RUN cd FFmpeg && ./configure \
    --enable-libnpp \
    --enable-nonfree \
    --enable-nvdec \
    --enable-nvenc \
    --enable-cuvid \
    --enable-cuda \
    --enable-cuda-nvcc \
    --enable-libvmaf \
    --enable-ffnvcodec \
    --disable-stripping \
    --extra-cflags="-I/usr/local/cuda/include" \
    --extra-ldflags="-L/usr/local/cuda/lib64 -L/usr/local/cuda/lib64/stubs/" 

RUN cd FFmpeg && make -j && make install

RUN mkdir /data
# VMAF+decode GPU (only works for NVDec supported formats https://developer.nvidia.com/video-encode-and-decode-gpu-support-matrix-new)
ENTRYPOINT ["ffmpeg" ,"-hwaccel", "cuda", "-hwaccel_output_format" ,"cuda", \
    "-i" ,"/data/ref.mp4", \
    "-hwaccel", "cuda", "-hwaccel_output_format", "cuda", \
    "-i", "/data/dis.mp4"  ,\
    "-filter_complex", "[0:v]scale_cuda=format=yuv420p[ref];[1:v]scale_cuda=format=yuv420p[dist];[dist][ref]libvmaf_cuda" ,"-f" ,"null", "-"]
