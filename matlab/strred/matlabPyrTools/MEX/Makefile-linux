MLAB = /usr/local/matlab5.1

MXSFX = mexlx
MEX = ${MLAB}/bin/mex

MFLAGS = -V4
INC = -I ${MLAB}/extern/include
LIB = -L ${MLAB}/extern/lib

CC = gcc -Wall -pedantic
C_OPTIMIZE_SWITCH = -O2    ## For GCC
CFLAGS = ${C_OPTIMIZE_SWITCH} ${INC} ${LIB}

all: corrDn.${MXSFX} upConv.${MXSFX} pointOp.${MXSFX} \
	histo.${MXSFX} range2.${MXSFX}

clean:
	/bin/rm *.o

corrDn.${MXSFX}: corrDn.o wrap.o convolve.o edges.o
	${MEX} ${MFLAGS} corrDn.o wrap.o convolve.o edges.o

upConv.${MXSFX}: upConv.o wrap.o convolve.o edges.o
	${MEX} ${MFLAGS} upConv.o wrap.o convolve.o edges.o

pointOp.${MXSFX}: pointOp.o
	${MEX} ${MFLAGS} pointOp.o

histo.${MXSFX}: histo.o
	${MEX} ${MFLAGS} histo.o

range2.${MXSFX}: range2.o
	${MEX} ${MFLAGS} range2.o

convolve.o wrap.o edges.o: convolve.h 

%.o : %.c
	${CC} -c ${CFLAGS} $<		

