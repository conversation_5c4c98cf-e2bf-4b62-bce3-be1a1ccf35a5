
This directory contains some Matlab script files that serve to give
example usage of this code, and also to explain some of the 
representations and algorithms.

The files are NOT meant to be executed from the MatLab prompt (like many 
of the MatLab demos).  You should instead read through the comments, 
executing the subsequent pieces of code.  This gives you a chance to 
explore as you go...

matlabPyrTools.m - Example usage of the code in the distribution.

pyramids.m - An introduction to multi-scale pyramid representations, 
 	covering Laplacian, QMF/Wavelet, and Steerable pyramids.  The
	file assumes a knowledge of linear systems, matrix algebra, 
	and 2D Fourier transforms.

more to come....
