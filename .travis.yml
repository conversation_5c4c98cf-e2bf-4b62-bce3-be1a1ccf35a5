os: linux
dist: jammy
language: c++
cache: ccache

addons:
  apt:
    update: true
    sources:
      - sourceline: "ppa:ubuntu-toolchain-r/test"
      - sourceline: "deb http://apt.llvm.org/jammy/ llvm-toolchain-jammy-11 main"
        key_url: "https://apt.llvm.org/llvm-snapshot.gpg.key"
    packages:
      - python3-pip
      - ninja-build
      - python3-setuptools
      - ccache
      - nasm
      - clang
  homebrew:
    packages:
      - ninja
      - ccache
      - nasm
    update: true

before_install: "sudo chown -R travis: $HOME/.ccache"

install:
  - ccache -s
  - pip3 install meson
  - |
    if type apt-get &>/dev/null; then
      case "$CC" in
      gcc-7) sudo apt-get install -qq gcc-7 g++-7 ;;
      gcc-8) sudo apt-get install -qq gcc-8 g++-8 ;;
      gcc-9) sudo apt-get install -qq gcc-9 g++-9 ;;
      esac
    fi
  - export CC="ccache $CC" CXX="ccache $CXX"

after_script:
  - ccache -s

matrix:
  fast_finish: true
  include:
    - name: libvmaf arm
      arch: arm64
      script: &base-gcc-script
        - mkdir libvmaf/build && cd libvmaf/build
        - meson .. --buildtype release -Denable_float=true
        - sudo ninja -v install
        - sudo ninja -v test
        - cd $TRAVIS_BUILD_DIR
