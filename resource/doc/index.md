# Documentation

This is an overview of the available documentation in the VMAF repository.

## FAQ

- [FAQ](faq.md) – a collection of frequently asked questions

## Models and Features

- [Models](models.md) – a summary of the available pre-trained models
- [Features](features.md) – VMAF's core features (metrics)
- [Datasets](datasets.md) – an overview of the two publicly available datasets for training custom models
- [Confidence Interval](conf_interval.md) – how to use bootstrapping to provide CI estimates for VMAF scores
- [Bad Cases](bad_cases.md) – how to report cases of VMAF not working well

## Software

- [Python library](python.md) – explains the Python wrapper for VMAF
- [ffmpeg](ffmpeg.md) – how to use VMAF in conjunction with FFmpeg
- [Docker](docker.md) – how to run VMAF with Docker
- [External resources](external_resource.md) – e.g. software using VMAF
- [MATLAB](matlab_usage.md) – running other quality algorithms (ST-RRED, ST-MAD, SpEED-QA, and BRISQUE) with MATLAB
- [Windows](windows.md) – how to build VMAF for Windows
- [AOM CTC](aom_ctc.md) - how to use VMAF compliant with [AOM](http://aomedia.org/) common test conditions.
- [NFLX CTC](nflx_ctc.md) - how to use NFLX common test conditions.

## Development

- [Release](release.md) – how to perform a new release

## Literature

- [References](references.md) – a list of links and papers
