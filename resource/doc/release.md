Instructions for Publishing a New Release
========================
- Manually search the repo for the current version number (for example: 1.5.1) and replace it with the next version (for example: 1.5.2).
- Update [CHANGELOG.md](https://github.com/Netflix/vmaf/blob/master/CHANGELOG.md) with new features and bugfixes since the last release (check against the [commit history](https://github.com/Netflix/vmaf/commits/master) between the dates and take in any significant / non-trivial changes into the "New features" and "Fixed bugs" lists).
- Merge the changes above into master. At the [release](https://github.com/Netflix/vmaf/releases) page, click on `Draft a new release`, and follow the instructions.
