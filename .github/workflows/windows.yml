name: Windows
on:
  push:
  pull_request:

jobs:
  build:
    runs-on: windows-2019
    strategy:
      fail-fast: false
      matrix:
        include:
          # Disabled 32-bit job due to vmaf score mismatch
          #- msystem: MINGW32
          #  MINGW_PACKAGE_PREFIX: mingw-w64-i686
          #  CFLAGS: -msse2 -mfpmath=sse -mstackrealign
          - msystem: MINGW64
            MINGW_PACKAGE_PREFIX: mingw-w64-x86_64
    env:
      CC: ccache gcc
      CXX: ccache g++
      CFLAGS: -pipe -static -O3 -mtune=generic -D_FILE_OFFSET_BITS=64 -mthreads ${{ matrix.CFLAGS }}
      CXXFLAGS: -pipe -static -O3 -mtune=generic -D_FILE_OFFSET_BITS=64 -mthreads ${{ matrix.CXXFLAGS }}
      LDFLAGS: -pipe -static -static-libgcc -static-libstdc++ ${{ matrix.LDFLAGS }}
    defaults:
      run:
        shell: msys2 {0}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Cache ccache files
        uses: actions/cache@v4
        with:
          path: |
            .ccache
          key: ${{ runner.os }}-${{ matrix.msystem }}-0-${{ hashFiles('**/*.c') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.msystem }}-0-
            ${{ runner.os }}-${{ matrix.msystem }}-

      - uses: msys2/setup-msys2@v2
        with:
          msystem: ${{ matrix.msystem }}
          install: ${{ matrix.MINGW_PACKAGE_PREFIX }}-ccache ${{ matrix.MINGW_PACKAGE_PREFIX }}-nasm ${{ matrix.MINGW_PACKAGE_PREFIX }}-pkg-config ${{ matrix.MINGW_PACKAGE_PREFIX }}-gcc ${{ matrix.MINGW_PACKAGE_PREFIX }}-meson ${{ matrix.MINGW_PACKAGE_PREFIX }}-ninja vim make diffutils
          update: true
          path-type: inherit

      - name: Set ccache dir
        run: echo "name=CCACHE_DIR::$PWD/.ccache" >> $GITHUB_ENV

      - name: Configure vmaf
        run: meson setup libvmaf libvmaf/build --buildtype release --default-library static --prefix "$MINGW_PREFIX"
      - name: Build vmaf
        run: meson install -C libvmaf/build

      - name: Test vmaf
        run: meson test -C libvmaf/build --num-processes $(nproc)

      - name: Get binary path & Current Release
        id: get_info
        run: |
          ldd "$MINGW_PREFIX/bin/vmaf.exe" || true
          echo "path=$(cygpath -m "$(command -v vmaf)")" >> $GITHUB_OUTPUT
          echo "upload_url=$(curl -L https://api.github.com/repos/${{ github.repository }}/releases/tags/$(cut -d/ -f3 <<< ${{ github.ref }}) | jq -r ."upload_url")" >> $GITHUB_OUTPUT

      - name: Upload vmaf
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.msystem }}-vmaf
          path: ${{ steps.get_info.outputs.path }}
      - name: Upload vmaf
        if: steps.get_info.outputs.upload_url != 'null' && matrix.msystem == 'MINGW64'
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.get_info.outputs.upload_url }}
          asset_path: ${{ steps.get_info.outputs.path }}
          asset_name: vmaf.exe
          asset_content_type: application/vnd.microsoft.portable-executable
