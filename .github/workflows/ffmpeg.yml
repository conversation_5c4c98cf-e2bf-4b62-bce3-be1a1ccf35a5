name: ffmpeg
on:
  push:
  pull_request:

env:
  DEBIAN_FRONTEND: noninteractive
  LD_LIBRARY_PATH: /usr/local/lib/x86_64-linux-gnu

jobs:
  ffmpeg:
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ubuntu-latest
            CC: gcc
            CXX: g++
          - os: macos-latest
            CC: clang
            CXX: clang++
    runs-on: ${{ matrix.os }}
    env:
      CC: ${{ matrix.CC }}
      CXX: ${{ matrix.CXX }}
    steps:
      - name: Setup python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11
      - name: Install meson and ninja
        run: |
          python -m pip install --upgrade pip
          pip install meson
      - name: Install dependencies (ubuntu)
        if: matrix.os == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo -E apt-get -yq install ninja-build gcc nasm
      - name: Install dependencies (mac)
        if: matrix.os == 'macos-latest'
        run: |
          brew install -q ninja nasm
      - uses: actions/checkout@v4
      - name: Build vmaf
        run: |
          meson setup libvmaf libvmaf/build --buildtype release
          sudo ninja -vC libvmaf/build install
      - name: Prepare FFmpeg
        run: |
          git clone -q --branch master --depth=1 "https://github.com/FFmpeg/FFmpeg" ffmpeg
          cd ffmpeg
          ./configure --enable-version3 --enable-libvmaf --disable-indevs --cc="$CC" --cxx="$CXX" || { less ffbuild/config.log; exit 1; }
      - name: Make FFmpeg
        run: |
          sudo make -C ffmpeg --quiet -j $(getconf _NPROCESSORS_ONLN 2>/dev/null || sysctl -n hw.ncpu) install
      - name: Test ffmpeg
        run: |
          curl "https://gist.githubusercontent.com/1480c1/0c4575da638ef6e8203feffd0597de16/raw/akiyo_cif.tar.xz.base64" | base64 -d | tar xJ
          vmaf_score=$(ffmpeg -hide_banner -nostats -i encoded.mkv -i orig.mkv -filter_complex libvmaf -f null - 2>&1 | grep 'VMAF score' | tr ' ' '\n' | tail -n1)
          echo "$vmaf_score"
          if [[ $vmaf_score != "93.663925" ]]; then
            echo "vmaf score doesn't match 93.663925"
            exit 1
          else
            echo "vmaf score matches"
            exit 0
          fi
        continue-on-error: true
