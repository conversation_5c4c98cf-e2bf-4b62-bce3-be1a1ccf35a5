/**
 *
 *  Copyright 2016-2020 Netflix, Inc.
 *
 *     Licensed under the BSD+Patent License (the "License");
 *     you may not use this file except in compliance with the License.
 *     You may obtain a copy of the License at
 *
 *         https://opensource.org/licenses/BSDplusPatent
 *
 *     Unless required by applicable law or agreed to in writing, software
 *     distributed under the License is distributed on an "AS IS" BASIS,
 *     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     See the License for the specific language governing permissions and
 *     limitations under the License.
 *
 */

#ifndef VMAF_VERSION_H
#define VMAF_VERSION_H

#define VMAF_API_VERSION_MAJOR @VMAF_API_VERSION_MAJOR@
#define VMAF_API_VERSION_MINOR @VMAF_API_VERSION_MINOR@
#define VMAF_API_VERSION_PATCH @VMAF_API_VERSION_PATCH@

#endif /* VMAF_VERSION_H */
