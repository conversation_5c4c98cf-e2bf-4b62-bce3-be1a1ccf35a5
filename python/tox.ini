[tox]
envlist = py311, coverage


[testenv]
passenv = APPVEYOR*
          CI
          SSL_CERT_FILE
          TEST_MARKER
          TRAVIS*
          PATH
          CC
          CXX
setenv = COVERAGE_FILE={toxworkdir}/.coverage.{envname}
usedevelop = True
deps = -rrequirements.txt
       -rtest/requirements.txt
# TODO: way too many warnings in tests, remove `-p no:warnings` and get rid of them all
commands = pytest {posargs:-vv -p no:warnings -m {env:TEST_MARKER:main} --doctest-modules --cov-report term-missing --cov=vmaf/}


[testenv:coverage]
passenv = {[testenv]passenv}
setenv = COVERAGE_FILE={toxworkdir}/.coverage
basepython = python
skip_install = True
deps = coverage
commands = coverage combine
           coverage report -i -m
           coverage xml -i -o "{toxworkdir}/coverage.xml"
           coverage html -i -d "{toxworkdir}/coverage"

# The 'venv*' sections are a handy way of creating a venv for local development (with python3 or python2)
[testenv:venv]
basepython = python3
envdir = .venv
usedevelop = True
deps = -rrequirements.txt
       -rtest/requirements.txt
commands = python --version

# Configuration
[pytest]
cache_dir = .tox/.cache
python_files = *test.py
markers =
    main: Main tests

[coverage:run]
source = vmaf
