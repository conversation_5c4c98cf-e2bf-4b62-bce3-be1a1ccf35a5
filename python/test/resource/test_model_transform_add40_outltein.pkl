(dp0
S'param_dict'
p1
(dp2
S'C'
p3
F2.5
sS'score_transform'
p4
(dp5
S'out_gte_in'
p6
S'true'
p7
sS'p0'
p8
F40.0
sS'p1'
p9
F1.0
sS'out_lte_in'
p10
g7
ssS'norm_type'
p11
S'custom_clip_0to1'
p12
sS'score_clip'
p13
(lp14
F0.0
aF100.0
asS'gamma'
p15
F0.025
sS'nu'
p16
F0.9
sS'custom_clip_0to1_map'
p17
(dp18
S'VMAF_feature_adm_scale0_score'
p19
(lp20
F0.0
aF0.5
asssS'model_dict'
p21
(dp22
g4
g5
sS'feature_dict'
p23
(dp24
S'VMAF_feature'
p25
(lp26
S'vif_scale0'
p27
aS'vif_scale1'
p28
aS'vif_scale2'
p29
aS'vif_scale3'
p30
aS'adm_scale0'
p31
aS'adm_scale1'
p32
aS'adm_scale2'
p33
aS'adm_scale3'
p34
aS'motion'
p35
assg11
S'linear_rescale'
p36
sg13
g14
sS'feature_names'
p37
(lp38
S'VMAF_feature_adm_scale0_score'
p39
aS'VMAF_feature_adm_scale1_score'
p40
aS'VMAF_feature_adm_scale2_score'
p41
aS'VMAF_feature_adm_scale3_score'
p42
aS'VMAF_feature_motion_score'
p43
aS'VMAF_feature_vif_scale0_score'
p44
aS'VMAF_feature_vif_scale1_score'
p45
aS'VMAF_feature_vif_scale2_score'
p46
aS'VMAF_feature_vif_scale3_score'
p47
asS'intercepts'
p48
(lp49
F-0.01818181818181818
aF0.0
aF-1.402605482765072
aF-0.9013780076860014
aF-2.4014359688539897
aF-0.03671084490620134
aF-0.08203910092775053
aF-0.2858058491124272
aF-0.480167543060334
aF-0.7764756920485143
asS'model_type'
p50
S'LIBSVMNUSVR'
p51
sS'model'
p52
NsS'slopes'
p53
(lp54
F0.009454545454545453
aF2.0
aF2.4026054827650722
aF1.9013780076860014
aF3.4014359688539897
aF0.05246778812837607
aF1.0820390945805258
aF1.2858061763913162
aF1.4801682848883448
aF1.7764765384047878
ass.